<?php
// 检查用户是否已登录
if (!isset($_SESSION['admin_user_id'])) {
    header('Location: login.php');
    exit;
}

// 获取当前选中的标签页
$current_tab = $_GET['tab'] ?? 'deepseek';
?>

<div class="app-customer-service-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1><i class="fas fa-robot"></i> APP客服</h1>
        <p>管理DeepSeek和豆包AI客服配置</p>
    </div>

    <!-- 顶部导航标签 -->
    <div class="app-customer-service-nav">
        <div class="nav-tabs">
            <button class="nav-tab <?php echo $current_tab === 'deepseek' ? 'active' : ''; ?>" 
                    onclick="switchTab('deepseek')" data-tab="deepseek">
                <i class="fas fa-brain"></i>
                <span>DeepSeek</span>
            </button>
            <button class="nav-tab <?php echo $current_tab === 'doubao' ? 'active' : ''; ?>" 
                    onclick="switchTab('doubao')" data-tab="doubao">
                <i class="fas fa-coffee"></i>
                <span>豆包</span>
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="app-customer-service-content">
        <!-- DeepSeek模块 -->
        <div id="deepseek-content" class="tab-content <?php echo $current_tab === 'deepseek' ? 'active' : ''; ?>">
            <?php include 'app_customer_service/deepseek_module.php'; ?>
        </div>

        <!-- 豆包模块 -->
        <div id="doubao-content" class="tab-content <?php echo $current_tab === 'doubao' ? 'active' : ''; ?>">
            <?php include 'app_customer_service/doubao_module.php'; ?>
        </div>
    </div>
</div>

<!-- APP客服专用样式 -->
<style>
.app-customer-service-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

/* 顶部导航标签 */
.app-customer-service-nav {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 8px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.nav-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.nav-tab.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.nav-tab i {
    font-size: 16px;
}

/* 内容区域 */
.app-customer-service-content {
    position: relative;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 模块卡片样式 */
.module-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.card-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: white;
    font-size: 18px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    color: white;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #ff6b9d;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #ff6b9d;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757, #c44569);
}

.btn-danger:hover {
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}
</style>

<!-- APP客服专用JavaScript -->
<script>
// 标签切换功能
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-content`).classList.add('active');
    
    // 更新URL参数
    const url = new URL(window.location);
    url.searchParams.set('tab', tabName);
    window.history.pushState({}, '', url);
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('tabChanged', { detail: { tab: tabName } }));
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('APP客服页面加载完成');

    // 监听浏览器后退/前进按钮
    window.addEventListener('popstate', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'deepseek';
        switchTab(tab);
    });
});

// 通用函数：显示Toast提示
function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
}

// 通用函数：渲染API密钥列表
function renderApiKeysList(provider, keys) {
    const container = document.getElementById(`${provider}-api-keys`);

    if (!keys || keys.length === 0) {
        container.innerHTML = `
            <div class="loading-placeholder">
                <i class="fas fa-key"></i>
                <span>暂无API密钥</span>
            </div>
        `;
        return;
    }

    const keysHtml = keys.map(key => `
        <div class="api-key-item">
            <div class="api-key-info">
                <div class="api-key-value">${maskApiKey(key.api_key)}</div>
                <div class="api-key-status">
                    <span class="status-badge ${key.status}">${key.status === 'active' ? '活跃' : '禁用'}</span>
                    <span class="key-stats">调用次数: ${key.call_count || 0}</span>
                </div>
            </div>
            <div class="api-key-actions">
                <button class="btn btn-sm btn-secondary" onclick="toggleApiKeyStatus('${provider}', ${key.id})">
                    <i class="fas fa-${key.status === 'active' ? 'pause' : 'play'}"></i>
                    ${key.status === 'active' ? '禁用' : '启用'}
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteApiKey('${provider}', ${key.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = keysHtml;
}

// 通用函数：掩码显示API密钥
function maskApiKey(apiKey) {
    if (!apiKey || apiKey.length < 8) return apiKey;
    return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4);
}

// 通用函数：显示添加API密钥模态框
function showAddApiKeyModal(provider) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> 添加${provider === 'deepseek' ? 'DeepSeek' : '豆包'} API密钥</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-api-key-form">
                    <div class="form-group">
                        <label class="form-label" for="api-key-input">API密钥</label>
                        <input type="text" class="form-control" id="api-key-input" name="api_key"
                               placeholder="请输入API密钥" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="api-key-name">密钥名称（可选）</label>
                        <input type="text" class="form-control" id="api-key-name" name="name"
                               placeholder="为密钥设置一个名称">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn" onclick="addApiKey('${provider}')">
                    <i class="fas fa-plus"></i> 添加
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

// 通用函数：关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => document.body.removeChild(modal), 300);
    }
}

// 通用函数：添加API密钥
function addApiKey(provider) {
    const form = document.getElementById('add-api-key-form');
    const formData = new FormData(form);

    if (!formData.get('api_key').trim()) {
        showToast('请输入API密钥', 'error');
        return;
    }

    fetch(`../../api/app_customer_service.php/${provider}/api-keys`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('API密钥添加成功！', 'success');
            closeModal();
            // 重新加载密钥列表
            if (provider === 'deepseek') {
                loadDeepSeekApiKeys();
            } else {
                loadDoubaoApiKeys();
            }
        } else {
            showToast(data.message || '添加失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
}

// 通用函数：切换API密钥状态
function toggleApiKeyStatus(provider, keyId) {
    fetch(`../../api/app_customer_service.php/${provider}/api-keys/${keyId}/toggle`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('密钥状态更新成功！', 'success');
            // 重新加载密钥列表
            if (provider === 'deepseek') {
                loadDeepSeekApiKeys();
            } else {
                loadDoubaoApiKeys();
            }
        } else {
            showToast(data.message || '操作失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
}

// 通用函数：删除API密钥
function deleteApiKey(provider, keyId) {
    if (!confirm('确定要删除这个API密钥吗？此操作不可恢复。')) {
        return;
    }

    fetch(`../../api/app_customer_service.php/${provider}/api-keys/${keyId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('API密钥删除成功！', 'success');
            // 重新加载密钥列表
            if (provider === 'deepseek') {
                loadDeepSeekApiKeys();
            } else {
                loadDoubaoApiKeys();
            }
        } else {
            showToast(data.message || '删除失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
}
</script>

<!-- Toast和Modal样式 -->
<style>
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    padding: 15px 20px;
    color: white;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left: 4px solid #2ecc71;
}

.toast-error {
    border-left: 4px solid #e74c3c;
}

.toast-info {
    border-left: 4px solid #3498db;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: white;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    border: 1px solid #2ecc71;
}

.status-badge.inactive {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.key-stats {
    margin-left: 10px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
}
</style>
