<div class="deepseek-module">
    <!-- 基本设置 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-brain"></i> DeepSeek 基本设置</h3>
            <p>配置DeepSeek AI客服的基本参数</p>
        </div>
        
        <form id="deepseek-basic-form">
            <div class="form-group">
                <label class="form-label">
                    <span>启用状态</span>
                    <label class="switch" style="float: right;">
                        <input type="checkbox" id="deepseek-enabled" name="enabled">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="deepseek-model">选择模型</label>
                <select class="form-control" id="deepseek-model" name="model">
                    <option value="">请选择模型</option>
                    <option value="deepseek-chat">deepseek-chat</option>
                    <option value="deepseek-reasoner">deepseek-reasoner</option>
                </select>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">
                    <i class="fas fa-save"></i> 保存基本设置
                </button>
            </div>
        </form>
    </div>

    <!-- API密钥管理 -->
    <div class="module-card">
        <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3><i class="fas fa-key"></i> API密钥管理</h3>
                    <p>管理DeepSeek的API密钥，支持多个密钥随机调用</p>
                </div>
                <button class="btn btn-secondary" onclick="showAddApiKeyModal('deepseek')">
                    <i class="fas fa-plus"></i> 添加密钥
                </button>
            </div>
        </div>
        
        <div class="api-keys-list" id="deepseek-api-keys">
            <!-- API密钥列表将通过JavaScript动态加载 -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 使用统计 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-chart-bar"></i> 使用统计</h3>
            <p>DeepSeek API调用统计信息</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="deepseek-total-calls">0</div>
                <div class="stat-label">总调用次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="deepseek-success-rate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="deepseek-avg-response">0ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="deepseek-active-keys">0</div>
                <div class="stat-label">活跃密钥数</div>
            </div>
        </div>
    </div>

    <!-- 测试连接 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-plug"></i> 连接测试</h3>
            <p>测试DeepSeek API连接状态</p>
        </div>
        
        <div class="test-section">
            <button class="btn" onclick="testDeepSeekConnection()">
                <i class="fas fa-play"></i> 测试连接
            </button>
            <div class="test-result" id="deepseek-test-result" style="display: none;">
                <!-- 测试结果将显示在这里 -->
            </div>
        </div>
    </div>
</div>

<!-- DeepSeek模块专用样式 -->
<style>
.deepseek-module {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #ff6b9d;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.api-keys-list {
    min-height: 100px;
}

.api-key-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.api-key-info {
    flex: 1;
}

.api-key-value {
    font-family: monospace;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 5px;
}

.api-key-status {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.api-key-actions {
    display: flex;
    gap: 10px;
}

.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.6);
}

.loading-placeholder i {
    font-size: 20px;
    margin-bottom: 10px;
    display: block;
}

.test-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.test-result {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-result.success {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.1);
}

.test-result.error {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.test-result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-weight: bold;
}

.test-result-body {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .api-key-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .api-key-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
</style>

<!-- DeepSeek模块专用JavaScript -->
<script>
// DeepSeek基本设置表单提交
document.addEventListener('DOMContentLoaded', function() {
    const deepseekForm = document.getElementById('deepseek-basic-form');
    if (deepseekForm) {
        deepseekForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDeepSeekBasicSettings();
        });
    }
    
    // 加载DeepSeek配置
    loadDeepSeekConfig();
    loadDeepSeekApiKeys();
    loadDeepSeekStats();
});

// 保存DeepSeek基本设置
function saveDeepSeekBasicSettings() {
    const formData = new FormData(document.getElementById('deepseek-basic-form'));
    
    fetch('../../api/app_customer_service.php/deepseek/config', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('DeepSeek设置保存成功！', 'success');
        } else {
            showToast(data.message || '保存失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
}

// 加载DeepSeek配置
function loadDeepSeekConfig() {
    fetch('../../api/app_customer_service.php/deepseek/config')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.config) {
            const config = data.config;
            document.getElementById('deepseek-enabled').checked = config.enabled || false;
            document.getElementById('deepseek-model').value = config.model || '';
        }
    })
    .catch(error => {
        console.error('Error loading DeepSeek config:', error);
    });
}

// 加载DeepSeek API密钥
function loadDeepSeekApiKeys() {
    fetch('../../api/app_customer_service.php/deepseek/api-keys')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderApiKeysList('deepseek', data.keys || []);
        }
    })
    .catch(error => {
        console.error('Error loading DeepSeek API keys:', error);
        document.getElementById('deepseek-api-keys').innerHTML = 
            '<div class="loading-placeholder"><i class="fas fa-exclamation-triangle"></i><span>加载失败</span></div>';
    });
}

// 加载DeepSeek统计信息
function loadDeepSeekStats() {
    fetch('../../api/app_customer_service.php/deepseek/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.stats) {
            const stats = data.stats;
            document.getElementById('deepseek-total-calls').textContent = stats.total_calls || 0;
            document.getElementById('deepseek-success-rate').textContent = (stats.success_rate || 0) + '%';
            document.getElementById('deepseek-avg-response').textContent = (stats.avg_response_time || 0) + 'ms';
            document.getElementById('deepseek-active-keys').textContent = stats.active_keys || 0;
        }
    })
    .catch(error => {
        console.error('Error loading DeepSeek stats:', error);
    });
}

// 测试DeepSeek连接
function testDeepSeekConnection() {
    const testButton = event.target;
    const testResult = document.getElementById('deepseek-test-result');
    
    // 显示加载状态
    testButton.disabled = true;
    testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    testResult.style.display = 'block';
    testResult.className = 'test-result';
    testResult.innerHTML = '<div class="test-result-header"><i class="fas fa-spinner fa-spin"></i> 正在测试连接...</div>';
    
    fetch('../../api/app_customer_service.php/deepseek/test', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        testButton.disabled = false;
        testButton.innerHTML = '<i class="fas fa-play"></i> 测试连接';
        
        if (data.success) {
            testResult.className = 'test-result success';
            testResult.innerHTML = `
                <div class="test-result-header">
                    <i class="fas fa-check-circle"></i> 连接成功
                </div>
                <div class="test-result-body">
                    响应时间: ${data.response_time}ms<br>
                    模型: ${data.model}<br>
                    状态: ${data.status}
                </div>
            `;
        } else {
            testResult.className = 'test-result error';
            testResult.innerHTML = `
                <div class="test-result-header">
                    <i class="fas fa-times-circle"></i> 连接失败
                </div>
                <div class="test-result-body">
                    错误信息: ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        testButton.disabled = false;
        testButton.innerHTML = '<i class="fas fa-play"></i> 测试连接';
        testResult.className = 'test-result error';
        testResult.innerHTML = `
            <div class="test-result-header">
                <i class="fas fa-times-circle"></i> 网络错误
            </div>
            <div class="test-result-body">
                ${error.message}
            </div>
        `;
    });
}
</script>
