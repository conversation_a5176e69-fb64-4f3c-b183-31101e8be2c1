<div class="doubao-module">
    <!-- 基本设置 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-coffee"></i> 豆包 基本设置</h3>
            <p>配置豆包AI客服的基本参数</p>
        </div>
        
        <form id="doubao-basic-form">
            <div class="form-group">
                <label class="form-label">
                    <span>启用状态</span>
                    <label class="switch" style="float: right;">
                        <input type="checkbox" id="doubao-enabled" name="enabled">
                        <span class="slider"></span>
                    </label>
                </label>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="doubao-model">选择模型</label>
                <select class="form-control" id="doubao-model" name="model">
                    <option value="">请选择模型</option>
                    <option value="doubao-seed-1-6-250615">doubao-seed-1-6-250615</option>
                    <option value="doubao-seed-1-6-flash-250715">doubao-seed-1-6-flash-250715</option>
                    <option value="doubao-1-5-thinking-pro-250415">doubao-1-5-thinking-pro-250415</option>
                </select>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">
                    <i class="fas fa-save"></i> 保存基本设置
                </button>
            </div>
        </form>
    </div>

    <!-- API密钥管理 -->
    <div class="module-card">
        <div class="card-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h3><i class="fas fa-key"></i> API密钥管理</h3>
                    <p>管理豆包的API密钥，支持多个密钥随机调用</p>
                </div>
                <button class="btn btn-secondary" onclick="showAddApiKeyModal('doubao')">
                    <i class="fas fa-plus"></i> 添加密钥
                </button>
            </div>
        </div>
        
        <div class="api-keys-list" id="doubao-api-keys">
            <!-- API密钥列表将通过JavaScript动态加载 -->
            <div class="loading-placeholder">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
        </div>
    </div>

    <!-- 使用统计 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-chart-bar"></i> 使用统计</h3>
            <p>豆包API调用统计信息</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="doubao-total-calls">0</div>
                <div class="stat-label">总调用次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="doubao-success-rate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="doubao-avg-response">0ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="doubao-active-keys">0</div>
                <div class="stat-label">活跃密钥数</div>
            </div>
        </div>
    </div>

    <!-- 测试连接 -->
    <div class="module-card">
        <div class="card-header">
            <h3><i class="fas fa-plug"></i> 连接测试</h3>
            <p>测试豆包API连接状态</p>
        </div>
        
        <div class="test-section">
            <button class="btn" onclick="testDoubaoConnection()">
                <i class="fas fa-play"></i> 测试连接
            </button>
            <div class="test-result" id="doubao-test-result" style="display: none;">
                <!-- 测试结果将显示在这里 -->
            </div>
        </div>
    </div>
</div>

<!-- 豆包模块专用样式 -->
<style>
.doubao-module {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* 豆包特有的主题色调 */
.doubao-module .stat-value {
    color: #f39c12;
}

.doubao-module .btn {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.doubao-module .btn:hover {
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.doubao-module .nav-tab.active {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.doubao-module input:checked + .slider {
    background-color: #f39c12;
}

.doubao-module .form-control:focus {
    border-color: #f39c12;
    box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}
</style>

<!-- 豆包模块专用JavaScript -->
<script>
// 豆包基本设置表单提交
document.addEventListener('DOMContentLoaded', function() {
    const doubaoForm = document.getElementById('doubao-basic-form');
    if (doubaoForm) {
        doubaoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveDoubaoBasicSettings();
        });
    }
    
    // 加载豆包配置
    loadDoubaoConfig();
    loadDoubaoApiKeys();
    loadDoubaoStats();
});

// 保存豆包基本设置
function saveDoubaoBasicSettings() {
    const formData = new FormData(document.getElementById('doubao-basic-form'));
    
    fetch('../../api/app_customer_service.php/doubao/config', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('豆包设置保存成功！', 'success');
        } else {
            showToast(data.message || '保存失败，请重试', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    });
}

// 加载豆包配置
function loadDoubaoConfig() {
    fetch('../../api/app_customer_service.php/doubao/config')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.config) {
            const config = data.config;
            document.getElementById('doubao-enabled').checked = config.enabled || false;
            document.getElementById('doubao-model').value = config.model || '';
        }
    })
    .catch(error => {
        console.error('Error loading Doubao config:', error);
    });
}

// 加载豆包API密钥
function loadDoubaoApiKeys() {
    fetch('../../api/app_customer_service.php/doubao/api-keys')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderApiKeysList('doubao', data.keys || []);
        }
    })
    .catch(error => {
        console.error('Error loading Doubao API keys:', error);
        document.getElementById('doubao-api-keys').innerHTML = 
            '<div class="loading-placeholder"><i class="fas fa-exclamation-triangle"></i><span>加载失败</span></div>';
    });
}

// 加载豆包统计信息
function loadDoubaoStats() {
    fetch('../../api/app_customer_service.php/doubao/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.stats) {
            const stats = data.stats;
            document.getElementById('doubao-total-calls').textContent = stats.total_calls || 0;
            document.getElementById('doubao-success-rate').textContent = (stats.success_rate || 0) + '%';
            document.getElementById('doubao-avg-response').textContent = (stats.avg_response_time || 0) + 'ms';
            document.getElementById('doubao-active-keys').textContent = stats.active_keys || 0;
        }
    })
    .catch(error => {
        console.error('Error loading Doubao stats:', error);
    });
}

// 测试豆包连接
function testDoubaoConnection() {
    const testButton = event.target;
    const testResult = document.getElementById('doubao-test-result');
    
    // 显示加载状态
    testButton.disabled = true;
    testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
    testResult.style.display = 'block';
    testResult.className = 'test-result';
    testResult.innerHTML = '<div class="test-result-header"><i class="fas fa-spinner fa-spin"></i> 正在测试连接...</div>';
    
    fetch('../../api/app_customer_service.php/doubao/test', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        testButton.disabled = false;
        testButton.innerHTML = '<i class="fas fa-play"></i> 测试连接';
        
        if (data.success) {
            testResult.className = 'test-result success';
            testResult.innerHTML = `
                <div class="test-result-header">
                    <i class="fas fa-check-circle"></i> 连接成功
                </div>
                <div class="test-result-body">
                    响应时间: ${data.response_time}ms<br>
                    模型: ${data.model}<br>
                    状态: ${data.status}
                </div>
            `;
        } else {
            testResult.className = 'test-result error';
            testResult.innerHTML = `
                <div class="test-result-header">
                    <i class="fas fa-times-circle"></i> 连接失败
                </div>
                <div class="test-result-body">
                    错误信息: ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        testButton.disabled = false;
        testButton.innerHTML = '<i class="fas fa-play"></i> 测试连接';
        testResult.className = 'test-result error';
        testResult.innerHTML = `
            <div class="test-result-header">
                <i class="fas fa-times-circle"></i> 网络错误
            </div>
            <div class="test-result-body">
                ${error.message}
            </div>
        `;
    });
}
</script>
