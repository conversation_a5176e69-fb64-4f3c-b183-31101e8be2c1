<?php
/**
 * APP客服API接口
 * 处理DeepSeek和豆包AI客服的配置和调用
 */

// 定义API访问常量
define('API_ACCESS', true);

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的文件
require_once __DIR__ . '/../includes/functions.php';

// 数据库连接
try {
    require_once __DIR__ . '/../includes/db.php';
    $database_available = is_database_available();
} catch (Exception $e) {
    $database_available = false;
    error_log('数据库连接失败: ' . $e->getMessage());
}

/**
 * APP客服API类
 */
class AppCustomerServiceAPI {
    private $db;
    private $method;
    private $endpoint;
    private $provider;
    private $action;
    private $id;
    
    public function __construct() {
        global $pdo, $database_available;
        
        if (!$database_available) {
            $this->sendError('Database connection failed', 500);
            exit;
        }
        
        $this->db = $pdo;
        $this->method = $_SERVER['REQUEST_METHOD'];
        
        // 解析URL路径
        $path = $_SERVER['PATH_INFO'] ?? $_SERVER['REQUEST_URI'] ?? '';
        $path = parse_url($path, PHP_URL_PATH);
        $path = trim($path, '/');
        
        // 移除脚本名称部分
        if (strpos($path, 'app_customer_service.php') !== false) {
            $path = substr($path, strpos($path, 'app_customer_service.php') + strlen('app_customer_service.php'));
            $path = trim($path, '/');
        }
        
        $parts = explode('/', $path);
        $this->provider = $parts[0] ?? '';
        $this->action = $parts[1] ?? '';
        $this->id = $parts[2] ?? '';
        
        // 初始化数据库表
        $this->initializeTables();
    }
    
    /**
     * 初始化数据库表
     */
    private function initializeTables() {
        try {
            // 检查表是否存在，如果不存在则创建
            $tables = [
                'app_customer_service_config',
                'app_customer_service_api_keys',
                'app_customer_service_stats'
            ];
            
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() == 0) {
                    // 表不存在，需要创建
                    $this->createTables();
                    break;
                }
            }
        } catch (Exception $e) {
            error_log("初始化数据库表失败: " . $e->getMessage());
        }
    }
    
    /**
     * 创建数据库表
     */
    private function createTables() {
        // 这里可以调用SQL脚本创建表，但为了简化，我们假设表已经存在
        // 实际部署时应该通过数据库迁移脚本来处理
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 验证提供商
            if (!in_array($this->provider, ['deepseek', 'doubao'])) {
                return $this->sendError('Invalid provider', 400);
            }
            
            switch ($this->action) {
                case 'config':
                    return $this->handleConfig();
                case 'api-keys':
                    return $this->handleApiKeys();
                case 'stats':
                    return $this->handleStats();
                case 'test':
                    return $this->handleTest();
                default:
                    return $this->sendError('Invalid action', 404);
            }
        } catch (Exception $e) {
            error_log('APP Customer Service API Error: ' . $e->getMessage());
            return $this->sendError('Internal server error', 500);
        }
    }
    
    /**
     * 处理配置请求
     */
    private function handleConfig() {
        switch ($this->method) {
            case 'GET':
                return $this->getConfig();
            case 'POST':
                return $this->saveConfig();
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取配置
     */
    private function getConfig() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM app_customer_service_config WHERE provider = ?");
            $stmt->execute([$this->provider]);
            $config = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$config) {
                return $this->sendError('Configuration not found', 404);
            }
            
            // 解析JSON配置数据
            if ($config['config_data']) {
                $config['config_data'] = json_decode($config['config_data'], true);
            }
            
            return $this->sendSuccess(['config' => $config]);
        } catch (Exception $e) {
            error_log("获取配置失败: " . $e->getMessage());
            return $this->sendError('Failed to get configuration', 500);
        }
    }
    
    /**
     * 保存配置
     */
    private function saveConfig() {
        try {
            $enabled = isset($_POST['enabled']) ? 1 : 0;
            $model = $_POST['model'] ?? null;
            
            $stmt = $this->db->prepare("
                UPDATE app_customer_service_config 
                SET enabled = ?, model = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE provider = ?
            ");
            $stmt->execute([$enabled, $model, $this->provider]);
            
            if ($stmt->rowCount() == 0) {
                // 如果没有更新任何行，尝试插入
                $stmt = $this->db->prepare("
                    INSERT INTO app_customer_service_config (provider, enabled, model) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$this->provider, $enabled, $model]);
            }
            
            return $this->sendSuccess(null, 'Configuration saved successfully');
        } catch (Exception $e) {
            error_log("保存配置失败: " . $e->getMessage());
            return $this->sendError('Failed to save configuration', 500);
        }
    }
    
    /**
     * 处理API密钥请求
     */
    private function handleApiKeys() {
        // 检查是否是切换状态的请求
        if ($this->method === 'POST' && $this->id && strpos($_SERVER['REQUEST_URI'], '/toggle') !== false) {
            return $this->toggleApiKeyStatus();
        }

        switch ($this->method) {
            case 'GET':
                return $this->getApiKeys();
            case 'POST':
                return $this->addApiKey();
            case 'DELETE':
                return $this->deleteApiKey();
            default:
                return $this->sendError('Method not allowed', 405);
        }
    }
    
    /**
     * 获取API密钥列表
     */
    private function getApiKeys() {
        try {
            $stmt = $this->db->prepare("
                SELECT id, name, api_key, status, call_count, last_used_at, created_at 
                FROM app_customer_service_api_keys 
                WHERE provider = ? 
                ORDER BY created_at DESC
            ");
            $stmt->execute([$this->provider]);
            $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 对API密钥进行掩码处理
            foreach ($keys as &$key) {
                $key['api_key'] = $this->maskApiKey($key['api_key']);
            }
            
            return $this->sendSuccess(['keys' => $keys]);
        } catch (Exception $e) {
            error_log("获取API密钥失败: " . $e->getMessage());
            return $this->sendError('Failed to get API keys', 500);
        }
    }
    
    /**
     * 添加API密钥
     */
    private function addApiKey() {
        try {
            $apiKey = $_POST['api_key'] ?? '';
            $name = $_POST['name'] ?? '';
            
            if (empty($apiKey)) {
                return $this->sendError('API key is required', 400);
            }
            
            // 加密存储API密钥（这里简化处理，实际应该使用更安全的加密方式）
            $encryptedKey = base64_encode($apiKey);
            
            $stmt = $this->db->prepare("
                INSERT INTO app_customer_service_api_keys (provider, name, api_key) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$this->provider, $name, $encryptedKey]);
            
            return $this->sendSuccess(null, 'API key added successfully');
        } catch (Exception $e) {
            error_log("添加API密钥失败: " . $e->getMessage());
            return $this->sendError('Failed to add API key', 500);
        }
    }
    
    /**
     * 删除API密钥
     */
    private function deleteApiKey() {
        try {
            if (empty($this->id)) {
                return $this->sendError('API key ID is required', 400);
            }

            $stmt = $this->db->prepare("
                DELETE FROM app_customer_service_api_keys
                WHERE id = ? AND provider = ?
            ");
            $stmt->execute([$this->id, $this->provider]);

            if ($stmt->rowCount() == 0) {
                return $this->sendError('API key not found', 404);
            }

            return $this->sendSuccess(null, 'API key deleted successfully');
        } catch (Exception $e) {
            error_log("删除API密钥失败: " . $e->getMessage());
            return $this->sendError('Failed to delete API key', 500);
        }
    }

    /**
     * 切换API密钥状态
     */
    private function toggleApiKeyStatus() {
        try {
            if (empty($this->id)) {
                return $this->sendError('API key ID is required', 400);
            }

            // 获取当前状态
            $stmt = $this->db->prepare("
                SELECT status FROM app_customer_service_api_keys
                WHERE id = ? AND provider = ?
            ");
            $stmt->execute([$this->id, $this->provider]);
            $current = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$current) {
                return $this->sendError('API key not found', 404);
            }

            // 切换状态
            $newStatus = $current['status'] === 'active' ? 'inactive' : 'active';

            $stmt = $this->db->prepare("
                UPDATE app_customer_service_api_keys
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND provider = ?
            ");
            $stmt->execute([$newStatus, $this->id, $this->provider]);

            return $this->sendSuccess(['status' => $newStatus], 'API key status updated successfully');
        } catch (Exception $e) {
            error_log("切换API密钥状态失败: " . $e->getMessage());
            return $this->sendError('Failed to toggle API key status', 500);
        }
    }
    
    /**
     * 处理统计请求
     */
    private function handleStats() {
        if ($this->method !== 'GET') {
            return $this->sendError('Method not allowed', 405);
        }
        
        try {
            // 获取基本统计信息
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
                    AVG(CASE WHEN status = 'success' THEN response_time END) as avg_response_time
                FROM app_customer_service_stats 
                WHERE provider = ?
            ");
            $stmt->execute([$this->provider]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // 获取活跃密钥数
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as active_keys 
                FROM app_customer_service_api_keys 
                WHERE provider = ? AND status = 'active'
            ");
            $stmt->execute([$this->provider]);
            $keyStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $result = [
                'total_calls' => (int)($stats['total_calls'] ?? 0),
                'success_rate' => $stats['total_calls'] > 0 ? 
                    round(($stats['success_calls'] / $stats['total_calls']) * 100, 2) : 0,
                'avg_response_time' => round($stats['avg_response_time'] ?? 0),
                'active_keys' => (int)($keyStats['active_keys'] ?? 0)
            ];
            
            return $this->sendSuccess(['stats' => $result]);
        } catch (Exception $e) {
            error_log("获取统计信息失败: " . $e->getMessage());
            return $this->sendError('Failed to get statistics', 500);
        }
    }
    
    /**
     * 处理测试请求
     */
    private function handleTest() {
        if ($this->method !== 'POST') {
            return $this->sendError('Method not allowed', 405);
        }
        
        try {
            // 获取配置
            $stmt = $this->db->prepare("SELECT * FROM app_customer_service_config WHERE provider = ?");
            $stmt->execute([$this->provider]);
            $config = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$config || !$config['enabled']) {
                return $this->sendError('Service is not enabled', 400);
            }
            
            // 获取一个活跃的API密钥
            $stmt = $this->db->prepare("
                SELECT api_key FROM app_customer_service_api_keys 
                WHERE provider = ? AND status = 'active' 
                ORDER BY RAND() LIMIT 1
            ");
            $stmt->execute([$this->provider]);
            $keyData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$keyData) {
                return $this->sendError('No active API key found', 400);
            }
            
            // 解密API密钥
            $apiKey = base64_decode($keyData['api_key']);
            
            // 模拟API调用测试（这里简化处理）
            $startTime = microtime(true);
            $testResult = $this->testApiConnection($this->provider, $apiKey, $config['model']);
            $responseTime = round((microtime(true) - $startTime) * 1000);
            
            if ($testResult['success']) {
                return $this->sendSuccess([
                    'response_time' => $responseTime,
                    'model' => $config['model'],
                    'status' => 'connected'
                ], 'Connection test successful');
            } else {
                return $this->sendError($testResult['message'], 400);
            }
        } catch (Exception $e) {
            error_log("测试连接失败: " . $e->getMessage());
            return $this->sendError('Connection test failed', 500);
        }
    }
    
    /**
     * 测试API连接
     */
    private function testApiConnection($provider, $apiKey, $model) {
        // 这里应该实现真实的API调用测试
        // 为了演示，我们返回模拟结果
        
        if (empty($apiKey)) {
            return ['success' => false, 'message' => 'API key is empty'];
        }
        
        if (empty($model)) {
            return ['success' => false, 'message' => 'Model is not selected'];
        }
        
        // 模拟成功的连接测试
        return ['success' => true, 'message' => 'Connection successful'];
    }
    
    /**
     * 掩码显示API密钥
     */
    private function maskApiKey($apiKey) {
        $decrypted = base64_decode($apiKey);
        if (strlen($decrypted) < 8) {
            return $decrypted;
        }
        return substr($decrypted, 0, 4) . '****' . substr($decrypted, -4);
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data = null, $message = 'Success') {
        $response = ['success' => true, 'message' => $message];
        if ($data) {
            $response = array_merge($response, $data);
        }
        echo json_encode($response);
        return true;
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode(['success' => false, 'message' => $message]);
        return false;
    }
}

// 处理请求
try {
    $api = new AppCustomerServiceAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
    error_log('APP Customer Service API Fatal Error: ' . $e->getMessage());
}
?>
