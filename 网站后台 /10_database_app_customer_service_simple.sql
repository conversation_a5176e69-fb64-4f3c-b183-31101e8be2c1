-- APP客服功能数据库表结构（简化版）
-- 包含DeepSeek和豆包的配置管理

-- 创建APP客服配置表
DROP TABLE IF EXISTS `app_customer_service_config`;
CREATE TABLE `app_customer_service_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `model` varchar(100) DEFAULT NULL COMMENT '选择的模型',
  `config_data` text DEFAULT NULL COMMENT '其他配置数据(JSON格式)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider` (`provider`),
  KEY `idx_provider` (`provider`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服配置表';

-- 创建API密钥管理表
DROP TABLE IF EXISTS `app_customer_service_api_keys`;
CREATE TABLE `app_customer_service_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `name` varchar(255) DEFAULT NULL COMMENT '密钥名称',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥（加密存储）',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '密钥状态',
  `call_count` int(11) NOT NULL DEFAULT 0 COMMENT '调用次数',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_status` (`provider`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服API密钥表';

-- 创建API调用统计表
DROP TABLE IF EXISTS `app_customer_service_stats`;
CREATE TABLE `app_customer_service_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `api_key_id` int(11) DEFAULT NULL COMMENT '使用的API密钥ID',
  `model` varchar(100) DEFAULT NULL COMMENT '使用的模型',
  `request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `status` enum('success','error','timeout') NOT NULL COMMENT '请求状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
  `cost` decimal(10,6) DEFAULT NULL COMMENT '调用成本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_status` (`provider`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服API调用统计表';

-- 插入默认配置数据
INSERT INTO `app_customer_service_config` (`provider`, `enabled`, `model`, `config_data`) VALUES
('deepseek', 0, NULL, '{"models":["deepseek-chat","deepseek-reasoner"],"api_endpoint":"https://api.deepseek.com/v1/chat/completions","default_params":{"temperature":0.7,"max_tokens":2000,"top_p":0.9}}'),
('doubao', 0, NULL, '{"models":["doubao-seed-1-6-250615","doubao-seed-1-6-flash-250715","doubao-1-5-thinking-pro-250415"],"api_endpoint":"https://ark.cn-beijing.volces.com/api/v3/chat/completions","default_params":{"temperature":0.7,"max_tokens":2000,"top_p":0.9}}');

-- 创建索引优化查询性能
CREATE INDEX `idx_stats_provider_time` ON `app_customer_service_stats` (`provider`, `request_time`);
CREATE INDEX `idx_stats_time_status` ON `app_customer_service_stats` (`request_time`, `status`);
CREATE INDEX `idx_api_keys_provider_status` ON `app_customer_service_api_keys` (`provider`, `status`);
