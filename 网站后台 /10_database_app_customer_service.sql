-- APP客服功能数据库表结构
-- 包含DeepSeek和豆包的配置管理

-- 创建APP客服配置表
DROP TABLE IF EXISTS `app_customer_service_config`;
CREATE TABLE `app_customer_service_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `model` varchar(100) DEFAULT NULL COMMENT '选择的模型',
  `config_data` json DEFAULT NULL COMMENT '其他配置数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider` (`provider`),
  KEY `idx_provider` (`provider`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服配置表';

-- 创建API密钥管理表
DROP TABLE IF EXISTS `app_customer_service_api_keys`;
CREATE TABLE `app_customer_service_api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `name` varchar(255) DEFAULT NULL COMMENT '密钥名称',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥（加密存储）',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '密钥状态',
  `call_count` int(11) NOT NULL DEFAULT 0 COMMENT '调用次数',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_status` (`provider`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服API密钥表';

-- 创建API调用统计表
DROP TABLE IF EXISTS `app_customer_service_stats`;
CREATE TABLE `app_customer_service_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider` varchar(50) NOT NULL COMMENT '服务提供商: deepseek, doubao',
  `api_key_id` int(11) DEFAULT NULL COMMENT '使用的API密钥ID',
  `model` varchar(100) DEFAULT NULL COMMENT '使用的模型',
  `request_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `status` enum('success','error','timeout') NOT NULL COMMENT '请求状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `tokens_used` int(11) DEFAULT NULL COMMENT '使用的token数量',
  `cost` decimal(10,6) DEFAULT NULL COMMENT '调用成本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_api_key_id` (`api_key_id`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_status` (`status`),
  KEY `idx_provider_status` (`provider`, `status`),
  CONSTRAINT `fk_stats_api_key` FOREIGN KEY (`api_key_id`) REFERENCES `app_customer_service_api_keys` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='APP客服API调用统计表';

-- 插入默认配置数据
INSERT INTO `app_customer_service_config` (`provider`, `enabled`, `model`, `config_data`) VALUES
('deepseek', 0, NULL, JSON_OBJECT(
    'models', JSON_ARRAY('deepseek-chat', 'deepseek-reasoner'),
    'api_endpoint', 'https://api.deepseek.com/v1/chat/completions',
    'default_params', JSON_OBJECT(
        'temperature', 0.7,
        'max_tokens', 2000,
        'top_p', 0.9
    )
)),
('doubao', 0, NULL, JSON_OBJECT(
    'models', JSON_ARRAY('doubao-seed-1-6-250615', 'doubao-seed-1-6-flash-250715', 'doubao-1-5-thinking-pro-250415'),
    'api_endpoint', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    'default_params', JSON_OBJECT(
        'temperature', 0.7,
        'max_tokens', 2000,
        'top_p', 0.9
    )
));

-- 创建视图：获取活跃的API密钥统计
CREATE OR REPLACE VIEW `v_active_api_keys_stats` AS
SELECT 
    provider,
    COUNT(*) as total_keys,
    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_keys,
    SUM(call_count) as total_calls,
    MAX(last_used_at) as last_used
FROM `app_customer_service_api_keys`
GROUP BY provider;

-- 创建视图：获取每日调用统计
CREATE OR REPLACE VIEW `v_daily_call_stats` AS
SELECT 
    provider,
    DATE(request_time) as call_date,
    COUNT(*) as total_calls,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_calls,
    AVG(CASE WHEN status = 'success' THEN response_time END) as avg_response_time,
    SUM(COALESCE(tokens_used, 0)) as total_tokens,
    SUM(COALESCE(cost, 0)) as total_cost
FROM `app_customer_service_stats`
WHERE request_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY provider, DATE(request_time)
ORDER BY call_date DESC;

-- 创建存储过程：获取提供商统计信息
DELIMITER //
CREATE PROCEDURE `GetProviderStats`(IN provider_name VARCHAR(50))
BEGIN
    DECLARE total_calls INT DEFAULT 0;
    DECLARE success_calls INT DEFAULT 0;
    DECLARE active_keys INT DEFAULT 0;
    DECLARE avg_response INT DEFAULT 0;
    
    -- 获取总调用次数
    SELECT COUNT(*) INTO total_calls 
    FROM app_customer_service_stats 
    WHERE provider = provider_name;
    
    -- 获取成功调用次数
    SELECT COUNT(*) INTO success_calls 
    FROM app_customer_service_stats 
    WHERE provider = provider_name AND status = 'success';
    
    -- 获取活跃密钥数
    SELECT COUNT(*) INTO active_keys 
    FROM app_customer_service_api_keys 
    WHERE provider = provider_name AND status = 'active';
    
    -- 获取平均响应时间
    SELECT COALESCE(AVG(response_time), 0) INTO avg_response 
    FROM app_customer_service_stats 
    WHERE provider = provider_name AND status = 'success';
    
    -- 返回结果
    SELECT 
        provider_name as provider,
        total_calls,
        success_calls,
        CASE WHEN total_calls > 0 THEN ROUND((success_calls / total_calls) * 100, 2) ELSE 0 END as success_rate,
        active_keys,
        ROUND(avg_response) as avg_response_time;
END //
DELIMITER ;

-- 创建触发器：更新API密钥使用统计
DELIMITER //
CREATE TRIGGER `update_api_key_usage` 
AFTER INSERT ON `app_customer_service_stats`
FOR EACH ROW
BEGIN
    IF NEW.api_key_id IS NOT NULL THEN
        UPDATE `app_customer_service_api_keys` 
        SET 
            call_count = call_count + 1,
            last_used_at = NEW.request_time
        WHERE id = NEW.api_key_id;
    END IF;
END //
DELIMITER ;

-- 创建索引优化查询性能
CREATE INDEX `idx_stats_provider_time` ON `app_customer_service_stats` (`provider`, `request_time`);
CREATE INDEX `idx_stats_time_status` ON `app_customer_service_stats` (`request_time`, `status`);
CREATE INDEX `idx_api_keys_provider_status` ON `app_customer_service_api_keys` (`provider`, `status`);

-- 添加表注释
ALTER TABLE `app_customer_service_config` COMMENT = 'APP客服配置表 - 存储DeepSeek和豆包的基本配置';
ALTER TABLE `app_customer_service_api_keys` COMMENT = 'APP客服API密钥表 - 管理多个API密钥';
ALTER TABLE `app_customer_service_stats` COMMENT = 'APP客服调用统计表 - 记录API调用详情';

-- 创建清理旧数据的事件（可选）
-- 每天清理30天前的统计数据
/*
CREATE EVENT IF NOT EXISTS `cleanup_old_stats`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    DELETE FROM `app_customer_service_stats` 
    WHERE `request_time` < DATE_SUB(NOW(), INTERVAL 30 DAY);
END;
*/
