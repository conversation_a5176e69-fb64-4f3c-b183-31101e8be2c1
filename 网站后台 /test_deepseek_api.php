<?php
/**
 * 测试DeepSeek API功能
 */

require_once 'includes/db.php';

echo "<h2>DeepSeek API功能测试</h2>\n";

// 测试1: 获取配置
echo "<h3>测试1: 获取DeepSeek配置</h3>\n";
try {
    $stmt = $pdo->prepare("SELECT * FROM app_customer_service_config WHERE provider = 'deepseek'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        echo "✓ 配置获取成功<br>\n";
        echo "- 提供商: " . $config['provider'] . "<br>\n";
        echo "- 启用状态: " . ($config['enabled'] ? '是' : '否') . "<br>\n";
        echo "- 选择模型: " . ($config['model'] ?: '未选择') . "<br>\n";
        echo "- 配置数据: " . substr($config['config_data'], 0, 100) . "...<br>\n";
    } else {
        echo "✗ 配置不存在<br>\n";
    }
} catch (Exception $e) {
    echo "✗ 获取配置失败: " . $e->getMessage() . "<br>\n";
}

// 测试2: 更新配置
echo "<h3>测试2: 更新DeepSeek配置</h3>\n";
try {
    $stmt = $pdo->prepare("
        UPDATE app_customer_service_config 
        SET enabled = 1, model = 'deepseek-chat', updated_at = CURRENT_TIMESTAMP 
        WHERE provider = 'deepseek'
    ");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "✓ 配置更新成功<br>\n";
    } else {
        echo "✗ 配置更新失败<br>\n";
    }
} catch (Exception $e) {
    echo "✗ 更新配置失败: " . $e->getMessage() . "<br>\n";
}

// 测试3: 添加API密钥
echo "<h3>测试3: 添加API密钥</h3>\n";
try {
    $testApiKey = base64_encode('sk-test-deepseek-key-' . time());
    $stmt = $pdo->prepare("
        INSERT INTO app_customer_service_api_keys (provider, name, api_key) 
        VALUES ('deepseek', '测试密钥', ?)
    ");
    $stmt->execute([$testApiKey]);
    
    echo "✓ API密钥添加成功<br>\n";
    echo "- 密钥ID: " . $pdo->lastInsertId() . "<br>\n";
} catch (Exception $e) {
    echo "✗ 添加API密钥失败: " . $e->getMessage() . "<br>\n";
}

// 测试4: 获取API密钥列表
echo "<h3>测试4: 获取API密钥列表</h3>\n";
try {
    $stmt = $pdo->prepare("
        SELECT id, name, api_key, status, call_count, created_at 
        FROM app_customer_service_api_keys 
        WHERE provider = 'deepseek' 
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✓ 获取API密钥列表成功<br>\n";
    echo "- 密钥数量: " . count($keys) . "<br>\n";
    
    foreach ($keys as $key) {
        $maskedKey = substr(base64_decode($key['api_key']), 0, 4) . '****' . substr(base64_decode($key['api_key']), -4);
        echo "- ID: {$key['id']}, 名称: {$key['name']}, 密钥: {$maskedKey}, 状态: {$key['status']}<br>\n";
    }
} catch (Exception $e) {
    echo "✗ 获取API密钥列表失败: " . $e->getMessage() . "<br>\n";
}

// 测试5: 获取统计信息
echo "<h3>测试5: 获取统计信息</h3>\n";
try {
    // 插入一些测试统计数据
    $stmt = $pdo->prepare("
        INSERT INTO app_customer_service_stats (provider, model, status, response_time) 
        VALUES ('deepseek', 'deepseek-chat', 'success', 1200)
    ");
    $stmt->execute();
    
    // 获取统计信息
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_calls,
            SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
            AVG(CASE WHEN status = 'success' THEN response_time END) as avg_response_time
        FROM app_customer_service_stats 
        WHERE provider = 'deepseek'
    ");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 获取活跃密钥数
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as active_keys 
        FROM app_customer_service_api_keys 
        WHERE provider = 'deepseek' AND status = 'active'
    ");
    $stmt->execute();
    $keyStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "✓ 统计信息获取成功<br>\n";
    echo "- 总调用次数: " . $stats['total_calls'] . "<br>\n";
    echo "- 成功调用次数: " . $stats['success_calls'] . "<br>\n";
    echo "- 成功率: " . ($stats['total_calls'] > 0 ? round(($stats['success_calls'] / $stats['total_calls']) * 100, 2) : 0) . "%<br>\n";
    echo "- 平均响应时间: " . round($stats['avg_response_time']) . "ms<br>\n";
    echo "- 活跃密钥数: " . $keyStats['active_keys'] . "<br>\n";
} catch (Exception $e) {
    echo "✗ 获取统计信息失败: " . $e->getMessage() . "<br>\n";
}

// 测试6: API接口测试
echo "<h3>测试6: API接口测试</h3>\n";
try {
    // 模拟API请求
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['PATH_INFO'] = '/deepseek/config';
    
    ob_start();
    include 'api/app_customer_service.php';
    $output = ob_get_clean();
    
    $result = json_decode($output, true);
    if ($result && $result['success']) {
        echo "✓ API接口测试成功<br>\n";
        echo "- 响应: " . substr($output, 0, 200) . "...<br>\n";
    } else {
        echo "✗ API接口测试失败<br>\n";
        echo "- 响应: " . $output . "<br>\n";
    }
} catch (Exception $e) {
    echo "✗ API接口测试失败: " . $e->getMessage() . "<br>\n";
}

echo "<h3>测试完成</h3>\n";
echo "<p><a href='xuxuemei/index.php?page=app_customer_service&tab=deepseek'>返回DeepSeek配置页面</a></p>\n";
?>
