<?php
/**
 * APP客服功能综合测试
 */

require_once 'includes/db.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>APP客服功能综合测试</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n";
echo ".test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo ".test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }\n";
echo ".test-section h3 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }\n";
echo ".success { color: #28a745; }\n";
echo ".error { color: #dc3545; }\n";
echo ".warning { color: #ffc107; }\n";
echo ".info { color: #17a2b8; }\n";
echo ".result { margin: 10px 0; padding: 10px; border-radius: 3px; }\n";
echo ".result.success { background: #d4edda; border: 1px solid #c3e6cb; }\n";
echo ".result.error { background: #f8d7da; border: 1px solid #f5c6cb; }\n";
echo ".result.warning { background: #fff3cd; border: 1px solid #ffeaa7; }\n";
echo ".result.info { background: #d1ecf1; border: 1px solid #bee5eb; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<div class='test-container'>\n";
echo "<h1>APP客服功能综合测试报告</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

// 测试1: 数据库连接和表结构
echo "<div class='test-section'>\n";
echo "<h3>1. 数据库连接和表结构测试</h3>\n";

try {
    $tables = ['app_customer_service_config', 'app_customer_service_api_keys', 'app_customer_service_stats'];
    foreach ($tables as $table) {
        $totalTests++;
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='result success'>✓ 表 $table 存在</div>\n";
            $passedTests++;
        } else {
            echo "<div class='result error'>✗ 表 $table 不存在</div>\n";
        }
    }
    
    // 检查配置数据
    $totalTests++;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM app_customer_service_config");
    $count = $stmt->fetch()['count'];
    if ($count >= 2) {
        echo "<div class='result success'>✓ 配置数据存在 ($count 条记录)</div>\n";
        $passedTests++;
    } else {
        echo "<div class='result error'>✗ 配置数据不足 ($count 条记录)</div>\n";
    }
} catch (Exception $e) {
    echo "<div class='result error'>✗ 数据库测试失败: " . $e->getMessage() . "</div>\n";
}

echo "</div>\n";

// 测试2: API接口测试
echo "<div class='test-section'>\n";
echo "<h3>2. API接口测试</h3>\n";

$providers = ['deepseek', 'doubao'];
$actions = ['config', 'api-keys', 'stats'];

foreach ($providers as $provider) {
    echo "<h4>$provider API测试</h4>\n";
    
    foreach ($actions as $action) {
        $totalTests++;
        try {
            // 模拟API请求
            $_SERVER['REQUEST_METHOD'] = 'GET';
            $_SERVER['PATH_INFO'] = "/$provider/$action";
            
            ob_start();
            include 'api/app_customer_service.php';
            $output = ob_get_clean();
            
            $result = json_decode($output, true);
            if ($result && $result['success']) {
                echo "<div class='result success'>✓ $provider/$action API 正常</div>\n";
                $passedTests++;
            } else {
                echo "<div class='result error'>✗ $provider/$action API 失败: " . ($result['message'] ?? '未知错误') . "</div>\n";
            }
        } catch (Exception $e) {
            echo "<div class='result error'>✗ $provider/$action API 异常: " . $e->getMessage() . "</div>\n";
        }
    }
}

echo "</div>\n";

// 测试3: 配置功能测试
echo "<div class='test-section'>\n";
echo "<h3>3. 配置功能测试</h3>\n";

foreach ($providers as $provider) {
    $totalTests++;
    try {
        // 测试配置更新
        $stmt = $pdo->prepare("UPDATE app_customer_service_config SET enabled = 1, model = ? WHERE provider = ?");
        $model = $provider === 'deepseek' ? 'deepseek-chat' : 'doubao-seed-1-6-250615';
        $stmt->execute([$model, $provider]);
        
        // 验证更新
        $stmt = $pdo->prepare("SELECT * FROM app_customer_service_config WHERE provider = ?");
        $stmt->execute([$provider]);
        $config = $stmt->fetch();
        
        if ($config && $config['enabled'] == 1 && $config['model'] === $model) {
            echo "<div class='result success'>✓ $provider 配置更新成功</div>\n";
            $passedTests++;
        } else {
            echo "<div class='result error'>✗ $provider 配置更新失败</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='result error'>✗ $provider 配置测试异常: " . $e->getMessage() . "</div>\n";
    }
}

echo "</div>\n";

// 测试4: API密钥管理测试
echo "<div class='test-section'>\n";
echo "<h3>4. API密钥管理测试</h3>\n";

foreach ($providers as $provider) {
    $totalTests += 3; // 添加、查询、删除
    
    try {
        // 添加测试密钥
        $testKey = base64_encode("test-$provider-key-" . time());
        $stmt = $pdo->prepare("INSERT INTO app_customer_service_api_keys (provider, name, api_key) VALUES (?, ?, ?)");
        $stmt->execute([$provider, "测试密钥", $testKey]);
        $keyId = $pdo->lastInsertId();
        
        echo "<div class='result success'>✓ $provider API密钥添加成功 (ID: $keyId)</div>\n";
        $passedTests++;
        
        // 查询密钥
        $stmt = $pdo->prepare("SELECT * FROM app_customer_service_api_keys WHERE id = ?");
        $stmt->execute([$keyId]);
        $key = $stmt->fetch();
        
        if ($key && $key['provider'] === $provider) {
            echo "<div class='result success'>✓ $provider API密钥查询成功</div>\n";
            $passedTests++;
        } else {
            echo "<div class='result error'>✗ $provider API密钥查询失败</div>\n";
        }
        
        // 删除密钥
        $stmt = $pdo->prepare("DELETE FROM app_customer_service_api_keys WHERE id = ?");
        $stmt->execute([$keyId]);
        
        if ($stmt->rowCount() > 0) {
            echo "<div class='result success'>✓ $provider API密钥删除成功</div>\n";
            $passedTests++;
        } else {
            echo "<div class='result error'>✗ $provider API密钥删除失败</div>\n";
        }
        
    } catch (Exception $e) {
        echo "<div class='result error'>✗ $provider API密钥管理测试异常: " . $e->getMessage() . "</div>\n";
    }
}

echo "</div>\n";

// 测试5: 统计功能测试
echo "<div class='test-section'>\n";
echo "<h3>5. 统计功能测试</h3>\n";

foreach ($providers as $provider) {
    $totalTests++;
    try {
        // 插入测试统计数据
        $stmt = $pdo->prepare("INSERT INTO app_customer_service_stats (provider, model, status, response_time) VALUES (?, ?, 'success', ?)");
        $model = $provider === 'deepseek' ? 'deepseek-chat' : 'doubao-seed-1-6-250615';
        $stmt->execute([$provider, $model, rand(500, 2000)]);
        
        // 查询统计信息
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_calls,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_calls,
                AVG(CASE WHEN status = 'success' THEN response_time END) as avg_response_time
            FROM app_customer_service_stats 
            WHERE provider = ?
        ");
        $stmt->execute([$provider]);
        $stats = $stmt->fetch();
        
        if ($stats && $stats['total_calls'] > 0) {
            echo "<div class='result success'>✓ $provider 统计功能正常 (总调用: {$stats['total_calls']}, 平均响应: " . round($stats['avg_response_time']) . "ms)</div>\n";
            $passedTests++;
        } else {
            echo "<div class='result error'>✗ $provider 统计功能异常</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='result error'>✗ $provider 统计测试异常: " . $e->getMessage() . "</div>\n";
    }
}

echo "</div>\n";

// 测试6: 前端页面测试
echo "<div class='test-section'>\n";
echo "<h3>6. 前端页面测试</h3>\n";

$pages = [
    'xuxuemei/index.php?page=app_customer_service&tab=deepseek' => 'DeepSeek配置页面',
    'xuxuemei/index.php?page=app_customer_service&tab=doubao' => '豆包配置页面',
    'demo_app_customer_service.php' => '演示页面'
];

foreach ($pages as $url => $name) {
    $totalTests++;
    if (file_exists($url) || strpos($url, '?') !== false) {
        echo "<div class='result success'>✓ $name 文件存在</div>\n";
        $passedTests++;
    } else {
        echo "<div class='result error'>✗ $name 文件不存在</div>\n";
    }
}

echo "</div>\n";

// 测试总结
echo "<div class='test-section'>\n";
echo "<h3>测试总结</h3>\n";

$successRate = $totalTests > 0 ? round(($passedTests / $totalTests) * 100, 2) : 0;

echo "<div class='result info'>\n";
echo "<strong>总测试数:</strong> $totalTests<br>\n";
echo "<strong>通过测试:</strong> $passedTests<br>\n";
echo "<strong>失败测试:</strong> " . ($totalTests - $passedTests) . "<br>\n";
echo "<strong>成功率:</strong> $successRate%<br>\n";
echo "</div>\n";

if ($successRate >= 90) {
    echo "<div class='result success'><strong>✓ 系统状态: 优秀</strong> - 所有主要功能正常工作</div>\n";
} elseif ($successRate >= 70) {
    echo "<div class='result warning'><strong>⚠ 系统状态: 良好</strong> - 大部分功能正常，建议检查失败项</div>\n";
} else {
    echo "<div class='result error'><strong>✗ 系统状态: 需要修复</strong> - 存在重要功能问题</div>\n";
}

echo "</div>\n";

// 快速链接
echo "<div class='test-section'>\n";
echo "<h3>快速链接</h3>\n";
echo "<p>\n";
echo "<a href='xuxuemei/index.php?page=app_customer_service&tab=deepseek' target='_blank'>DeepSeek配置</a> | \n";
echo "<a href='xuxuemei/index.php?page=app_customer_service&tab=doubao' target='_blank'>豆包配置</a> | \n";
echo "<a href='demo_app_customer_service.php' target='_blank'>功能演示</a> | \n";
echo "<a href='test_deepseek_api.php' target='_blank'>DeepSeek测试</a> | \n";
echo "<a href='test_doubao_api.php' target='_blank'>豆包测试</a>\n";
echo "</p>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
